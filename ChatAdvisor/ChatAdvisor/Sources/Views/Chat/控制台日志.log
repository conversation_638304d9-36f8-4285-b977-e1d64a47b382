LookinServer - Will launch. Framework version: 1.2.8
11.15.0 - [FirebaseCore][I-COR000003] The default Firebase app has not yet been configured. Add `FirebaseApp.configure()` to your application initialization. This can be done in in the App Delegate's application(_:didFinishLaunchingWithOptions:)` (or the `@main` struct's initializer in SwiftUI). Read more: https://firebase.google.com/docs/ios/setup#initialize_firebase_in_your_app
开始设置数据库...
创建数据库连接: /var/mobile/Containers/Data/Application/74AE2E55-9FA4-42BC-8D3E-960DABBB7898/Documents/ChatDatabase_7552777466750264080b5c1280e59a12.db
开始创建数据库表和索引...
Database tables created successfully.
创建初始数据库版本记录: v1
Database indexes created successfully.
Database tables created successfully.
数据库设置完成，发送完成通知
LookinServer - Searching port to listen...
LookinServer - Connected successfully on 127.0.0.1:47175
数据库已准备就绪
数据库初始化完成
数据库完整性检查完成
11.15.0 - [FirebaseCore][I-COR000003] The default Firebase app has not yet been configured. Add `FirebaseApp.configure()` to your application initialization. This can be done in in the App Delegate's application(_:didFinishLaunchingWithOptions:)` (or the `@main` struct's initializer in SwiftUI). Read more: https://firebase.google.com/docs/ios/setup#initialize_firebase_in_your_app
用户配置加载完成
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getConfig
版本检测已禁用或版本控制信息为空
版本检测已禁用或版本控制信息为空
开始清理空会话...
App is being debugged, do not track this hang
Hang detected: 0.45s (debugger attached, not reporting)
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getConfig
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getProducts
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getProducts
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getPricing
App is being debugged, do not track this hang
Hang detected: 0.63s (debugger attached, not reporting)
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getPricing
App is being debugged, do not track this hang
Hang detected: 0.53s (debugger attached, not reporting)
版本检测已禁用或版本控制信息为空
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_refreshToken
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 请求成功_getConfig
服务器配置加载完成
预加载会话数据完成: 3201C184-D6F6-4998-8093-664820B91685
性能监控已启动
用户界面准备完成
App is being debugged, do not track this hang
Hang detected: 0.36s (debugger attached, not reporting)
所有初始化步骤完成
App is being debugged, do not track this hang
Hang detected: 0.32s (debugger attached, not reporting)
App is being debugged, do not track this hang
Hang detected: 0.72s (debugger attached, not reporting)
主动检查版本更新...
距离上次检查：20.867815小时，是否需要检查：false
跳过版本检查：未到检查时间或已检查过
开始执行初始数据加载
App is being debugged, do not track this hang
Hang detected: 0.73s (debugger attached, not reporting)
等待数据库准备完成...
数据库已准备就绪
数据库已准备完成，开始加载会话
添加新会话到分组: 有效会话数量=20
会话已添加到分组: chatId=3201C184-D6F6-4998-8093-664820B91685, date=2025-07-31 16:00:00 +0000
会话已添加到分组: chatId=AB90B57D-B071-46ED-B2F6-594C76568FAA, date=2025-07-31 16:00:00 +0000
会话已添加到分组: chatId=24422F21-B04E-455B-882E-DFD7D96FCF7E, date=2025-07-31 16:00:00 +0000
会话已添加到分组: chatId=653A805C-6758-4B5A-B3BC-F5D1CED27EC0, date=2025-07-28 16:00:00 +0000
会话已添加到分组: chatId=8A14E455-974B-444B-A000-9A024B89B70E, date=2025-07-02 16:00:00 +0000
会话已添加到分组: chatId=2CB39E64-E0E4-4DF2-8D94-40F4BEAC9510, date=2025-07-02 16:00:00 +0000
会话已添加到分组: chatId=1F6FB966-6D22-48D0-8265-D7702AD13D0F, date=2025-07-01 16:00:00 +0000
会话已添加到分组: chatId=D2356847-B560-4278-9DBE-3963CFBF27D2, date=2025-07-01 16:00:00 +0000
会话已添加到分组: chatId=A6A7F647-07F6-4EAE-93B9-382005397A5E, date=2025-07-01 16:00:00 +0000
会话已添加到分组: chatId=EE07127F-D31A-4E63-9FEE-8E9435211FCE, date=2025-07-01 16:00:00 +0000
会话已添加到分组: chatId=7C403EB2-B138-4658-9AEF-D3DD022C80CF, date=2025-07-01 16:00:00 +0000
会话已添加到分组: chatId=10F1D4FB-49B2-4CF1-BBF1-2E58CE81ED45, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=24120D4D-1917-4306-8A20-321647A101C0, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=A907B732-3A99-42DD-B1E2-75B9217CE980, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=59C0ACFA-2F69-4FE3-BDE6-F7C34F5F7496, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=6D056734-C988-48B2-8B57-D71E3A22008E, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=B081A9B4-BEB0-4B49-A390-68DFB7DCC10F, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=9CD5404A-5A7A-45A7-841E-796BE199FEFC, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=62F1C917-4F8D-40CB-BC0B-3194D334DD95, date=2025-06-30 16:00:00 +0000
会话已添加到分组: chatId=5E2C6C2A-1230-41A5-A314-498765E1FD62, date=2025-06-30 16:00:00 +0000
成功加载 20 个会话
数据库已准备就绪
初始数据加载完成
开始记录操作: session_switch_3201C184-D6F6-4998-8093-664820B91685_A057CE1B, 类型: 会话切换
数据库已准备就绪
UI状态已重置
操作完成: session_switch_3201C184-D6F6-4998-8093-664820B91685_A057CE1B, 耗时: 0.064秒
开始加载初始消息
磁盘缓存写入成功: 46E70D13-8784-4DCF-B6A0-BE6AC34D9F63
缓存设置完成: 46E70D13-8784-4DCF-B6A0-BE6AC34D9F63
磁盘缓存写入成功: 40EAEEED-1939-4804-AC14-06F0CB7084B1
缓存设置完成: 40EAEEED-1939-4804-AC14-06F0CB7084B1
数据库已准备就绪
加载初始消息成功: 2 条
完成加载初始消息
网络状态更新: 可用
预加载会话成功: AB90B57D-B071-46ED-B2F6-594C76568FAA
数据库已准备就绪
预加载会话成功: 24422F21-B04E-455B-882E-DFD7D96FCF7E
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 新聊天
App is being debugged, do not track this hang
Hang detected: 0.46s (debugger attached, not reporting)
同步保存当前会话: chatId=95622F88-4A0E-436E-882B-310DA8118643, title=没有标题的会话
更新会话列表: chatId=95622F88-4A0E-436E-882B-310DA8118643, hasUserMessages=false, messagesCount=0
会话列表已更新: chatId=95622F88-4A0E-436E-882B-310DA8118643
更新会话列表: chatId=95622F88-4A0E-436E-882B-310DA8118643, hasUserMessages=false, messagesCount=0
开始原子化会话选择: chatId=95622F88-4A0E-436E-882B-310DA8118643
开始记录操作: session_switch_95622F88-4A0E-436E-882B-310DA8118643_CF8DF495, 类型: 会话切换
数据库已准备就绪
UI状态已重置
操作完成: session_switch_95622F88-4A0E-436E-882B-310DA8118643_CF8DF495, 耗时: 0.035秒
会话选择成功完成: chatId=95622F88-4A0E-436E-882B-310DA8118643
新会话创建完成并成功切换: chatId=95622F88-4A0E-436E-882B-310DA8118643
开始加载初始消息
加载初始消息成功: 0 条
完成加载初始消息
Bound preference ScrollOffsetPreferenceKey tried to update multiple times per frame.
开始原子化会话选择: chatId=3201C184-D6F6-4998-8093-664820B91685
开始记录操作: session_switch_3201C184-D6F6-4998-8093-664820B91685_3BEEC5AC, 类型: 会话切换
操作完成: session_switch_3201C184-D6F6-4998-8093-664820B91685_3BEEC5AC, 耗时: 0.000秒
会话选择成功完成: chatId=3201C184-D6F6-4998-8093-664820B91685
开始原子化会话选择: chatId=AB90B57D-B071-46ED-B2F6-594C76568FAA
开始记录操作: session_switch_AB90B57D-B071-46ED-B2F6-594C76568FAA_C6B12F91, 类型: 会话切换
UI状态已重置
操作完成: session_switch_AB90B57D-B071-46ED-B2F6-594C76568FAA_C6B12F91, 耗时: 0.027秒
会话选择成功完成: chatId=AB90B57D-B071-46ED-B2F6-594C76568FAA
开始加载初始消息
加载初始消息成功: 2 条
完成加载初始消息
磁盘缓存写入成功: 83947B8C-06E3-4240-A747-2779D1A91259
磁盘缓存写入成功: 37B2B57A-083F-4397-8A30-0E9A0AA8B09A
缓存设置完成: 37B2B57A-083F-4397-8A30-0E9A0AA8B09A
缓存设置完成: 83947B8C-06E3-4240-A747-2779D1A91259
开始原子化会话选择: chatId=24422F21-B04E-455B-882E-DFD7D96FCF7E
开始记录操作: session_switch_24422F21-B04E-455B-882E-DFD7D96FCF7E_3D5C9141, 类型: 会话切换
UI状态已重置
操作完成: session_switch_24422F21-B04E-455B-882E-DFD7D96FCF7E_3D5C9141, 耗时: 0.003秒
会话选择成功完成: chatId=24422F21-B04E-455B-882E-DFD7D96FCF7E
开始加载初始消息
磁盘缓存写入成功: B168887C-4028-4B3C-A2DF-85A2302864B0
缓存设置完成: B168887C-4028-4B3C-A2DF-85A2302864B0
磁盘缓存写入成功: F1B564FC-1385-411B-893E-C20650EC29AE
磁盘缓存写入成功: 60AE4344-B67D-435C-9628-A38A089B1ED9
加载初始消息成功: 4 条
完成加载初始消息
缓存设置完成: F1B564FC-1385-411B-893E-C20650EC29AE
缓存设置完成: 60AE4344-B67D-435C-9628-A38A089B1ED9
磁盘缓存写入成功: 3F07A476-BD62-4363-AC71-F6B0EF13C4B8
缓存设置完成: 3F07A476-BD62-4363-AC71-F6B0EF13C4B8
开始原子化会话选择: chatId=3201C184-D6F6-4998-8093-664820B91685
开始记录操作: session_switch_3201C184-D6F6-4998-8093-664820B91685_A79413B1, 类型: 会话切换
操作完成: session_switch_3201C184-D6F6-4998-8093-664820B91685_A79413B1, 耗时: 0.000秒
会话选择成功完成: chatId=3201C184-D6F6-4998-8093-664820B91685
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 新聊天
同步保存当前会话: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130, title=没有标题的会话
更新会话列表: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130, hasUserMessages=false, messagesCount=0
会话列表已更新: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130
更新会话列表: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130, hasUserMessages=false, messagesCount=0
开始原子化会话选择: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130
开始记录操作: session_switch_5FD6BFC6-489C-4790-B6AE-EC753CCD0130_72CEA9F8, 类型: 会话切换
数据库已准备就绪
UI状态已重置
操作完成: session_switch_5FD6BFC6-489C-4790-B6AE-EC753CCD0130_72CEA9F8, 耗时: 0.034秒
会话选择成功完成: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130
新会话创建完成并成功切换: chatId=5FD6BFC6-489C-4790-B6AE-EC753CCD0130
开始加载初始消息
加载初始消息成功: 0 条
完成加载初始消息
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
[u CF2EF503-DF26-4BEE-B293-6EBDBE770B59:m (null)] [com.sogou.sogouinput.basekeyboard(12.8.0)] RB query for the extension process state failed with error: Error Domain=RBSServiceErrorDomain Code=1 "Client not entitled" UserInfo={RBSEntitlement=com.apple.runningboard.process-state, NSLocalizedFailureReason=Client not entitled, RBSPermanent=false}
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
Received external candidate resultset. Total number of candidates: 0
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
Received external candidate resultset. Total number of candidates: 16
App is being debugged, do not track this hang
Hang detected: 0.70s (debugger attached, not reporting)
Received external candidate resultset. Total number of candidates: 16
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
Result accumulator timeout: 0.250000, exceeded.
resultToPush is nil, will not push anything to candidate receiver..
Unknown client: ChatAdvisor
App is being debugged, do not track this hang
Hang detected: 1.06s (debugger attached, not reporting)
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
App is being debugged, do not track this hang
Hang detected: 0.25s (debugger attached, not reporting)
App is being debugged, do not track this hang
Hang detected: 0.25s (debugger attached, not reporting)
Class CKBrowserSwitcherViewController overrides the -traitCollection getter, which is not supported. If you're trying to override traits, you must use the appropriate API.
App is being debugged, do not track this hang
Hang detected: 1.15s (debugger attached, not reporting)
Received external candidate resultset. Total number of candidates: 16
保存消息成功: 23074C0C-0D68-45B9-9D35-ABAC75804F40
消息保存成功: 23074C0C-0D68-45B9-9D35-ABAC75804F40
断开SSE连接
磁盘缓存写入成功: 23074C0C-0D68-45B9-9D35-ABAC75804F40
缓存设置完成: 23074C0C-0D68-45B9-9D35-ABAC75804F40
11.15.0 - <AppMeasurement>[I-ACS025018] Event not logged. FirebaseApp not configured.: 聊天_local
网络连接恢复
建立连接: http://10.0.136.252:33001/chat
连接建立成功
开始SSE连接: http://10.0.136.252:33001/chat
收到HTTP响应，状态码: 200
SSE连接已建立
接收到原始数据 (542 字节): data: {"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":nu...
处理接收数据: 'data: {"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


data: {"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 540)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


data: {"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 540)
使用标准SSE格式处理
分割后行数: 7
保留最后一行: ''
完整行数: 2
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"\",\"role\":\"assistant\"},\"index\":0,\"logprobs\":null,\"finish_reason\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}", "data: {\"choices\":[{\"finish_reason\":null,\"logprobs\":null,\"delta\":{\"content\":\"嘿\"},\"index\":0}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 2
处理第1行: 'data: {"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 270)
完整JSON字符串: '{"choices":[{"delta":{"content":"","role":"assistant"},"index":0,"logprobs":null,"finish_reason":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
处理第2行: 'data: {"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 252)
完整JSON字符串: '{"choices":[{"finish_reason":null,"logprobs":null,"delta":{"content":"嘿"},"index":0}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
开始打字机效果
添加内容到打字机队列: 1 字符，总长度: 1
LookinServer - channel:[1-47175,Listening], acceptConnection:[2-0,Connected]
LookinServer - Ignore channel[1-47175,Listening] end.
打字机效果完成
消息显示完成: 899875A0-917D-4001-B2AB-33DC5FB27D74, 内容长度: 0
接收到原始数据 (272 字节): data: {"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwe...
处理接收数据: 'data: {"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 264)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 264)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"，你知道\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 255)
完整JSON字符串: '{"choices":[{"delta":{"content":"，你知道"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 4 字符，总长度: 5
接收到原始数据 (269 字节): data: {"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen...
处理接收数据: 'data: {"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 263)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 263)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"吗，我\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 254)
完整JSON字符串: '{"choices":[{"delta":{"content":"吗，我"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 3 字符，总长度: 8
接收到原始数据 (266 字节): data: {"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-...
处理接收数据: 'data: {"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 262)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 262)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"昨天\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 253)
完整JSON字符串: '{"choices":[{"delta":{"content":"昨天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 2 字符，总长度: 10
接收到原始数据 (284 字节): data: {"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":...
处理接收数据: 'data: {"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 268)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 268)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"晚上一直在想你说\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 259)
完整JSON字符串: '{"choices":[{"delta":{"content":"晚上一直在想你说"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 8 字符，总长度: 18
接收到原始数据 (275 字节): data: {"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qw...
处理接收数据: 'data: {"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 265)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 265)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"的那句话，\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 256)
完整JSON字符串: '{"choices":[{"delta":{"content":"的那句话，"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 5 字符，总长度: 23
接收到原始数据 (275 字节): data: {"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qw...
处理接收数据: 'data: {"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 265)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 265)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"越想越觉得\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 256)
完整JSON字符串: '{"choices":[{"delta":{"content":"越想越觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 5 字符，总长度: 28
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"有意思。其实生活中\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"有意思。其实生活中"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 37
接收到原始数据 (293 字节): data: {"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"mode...
处理接收数据: 'data: {"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 271)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 271)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"很多时候都是这样，表面\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 262)
完整JSON字符串: '{"choices":[{"delta":{"content":"很多时候都是这样，表面"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 11 字符，总长度: 48
接收到原始数据 (278 字节): data: {"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"q...
处理接收数据: 'data: {"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 266)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 266)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"看似平常，但\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 257)
完整JSON字符串: '{"choices":[{"delta":{"content":"看似平常，但"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 6 字符，总长度: 54
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"仔细回味却藏着\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"仔细回味却藏着"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 61
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"很多细节。比如\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"很多细节。比如"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 68
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"我们上次一起喝\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"我们上次一起喝"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 75
接收到原始数据 (290 字节): data: {"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model...
处理接收数据: 'data: {"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 270)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 270)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"咖啡的那个下午，阳光\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 261)
完整JSON字符串: '{"choices":[{"delta":{"content":"咖啡的那个下午，阳光"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 10 字符，总长度: 85
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"正好，你说话的语气\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"正好，你说话的语气"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 94
接收到原始数据 (278 字节): data: {"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"q...
处理接收数据: 'data: {"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 266)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 266)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"和表情，现在\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 257)
完整JSON字符串: '{"choices":[{"delta":{"content":"和表情，现在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 6 字符，总长度: 100
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"回想起来都特别\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"回想起来都特别"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 107
接收到原始数据 (278 字节): data: {"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"q...
处理接收数据: 'data: {"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 266)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 266)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"清晰。我是个\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 257)
完整JSON字符串: '{"choices":[{"delta":{"content":"清晰。我是个"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 6 字符，总长度: 113
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"比较慢热的人，但在\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"比较慢热的人，但在"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 122
性能分析报告:
性能指标报告:
- 平均会话切换时间: 0.000秒
- 缓存命中率: 57.1%
- 错误率: 0.0%
- 总操作数: 7
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"你面前好像慢慢\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"你面前好像慢慢"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 129
接收到原始数据 (284 字节): data: {"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":...
处理接收数据: 'data: {"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 268)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 268)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"学会了更直接表达\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 259)
完整JSON字符串: '{"choices":[{"delta":{"content":"学会了更直接表达"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 8 字符，总长度: 137
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"。有时候我也会纠结\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"。有时候我也会纠结"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 146
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"自己是不是说得太多\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"自己是不是说得太多"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 155
接收到原始数据 (275 字节): data: {"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qw...
处理接收数据: 'data: {"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 265)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 265)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"，但又觉得\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 256)
完整JSON字符串: '{"choices":[{"delta":{"content":"，但又觉得"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 5 字符，总长度: 160
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"，如果不把这些想法\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"，如果不把这些想法"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 169
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"告诉你，好像就\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"告诉你，好像就"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 176
接收到原始数据 (278 字节): data: {"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"q...
处理接收数据: 'data: {"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 266)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 266)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"少了点什么。\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 257)
完整JSON字符串: '{"choices":[{"delta":{"content":"少了点什么。"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 6 字符，总长度: 182
接收到原始数据 (287 字节): data: {"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 269)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 269)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"你是不是也有这样的\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 260)
完整JSON字符串: '{"choices":[{"delta":{"content":"你是不是也有这样的"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 9 字符，总长度: 191
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"感觉，就是某些\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"感觉，就是某些"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 198
接收到原始数据 (278 字节): data: {"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"q...
处理接收数据: 'data: {"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 266)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 266)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"话不管怎么藏\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 257)
完整JSON字符串: '{"choices":[{"delta":{"content":"话不管怎么藏"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 6 字符，总长度: 204
接收到原始数据 (296 字节): data: {"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"mod...
处理接收数据: 'data: {"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 272)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 272)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"，最后还是会忍不住说出来\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 263)
完整JSON字符串: '{"choices":[{"delta":{"content":"，最后还是会忍不住说出来"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 12 字符，总长度: 216
接收到原始数据 (281 字节): data: {"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"...
处理接收数据: 'data: {"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 267)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 267)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"？我觉得这大概\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 258)
完整JSON字符串: '{"choices":[{"delta":{"content":"？我觉得这大概"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 7 字符，总长度: 223
接收到原始数据 (293 字节): data: {"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"mode...
处理接收数据: 'data: {"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 271)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 271)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"就是相处的一部分吧。对\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 262)
完整JSON字符串: '{"choices":[{"delta":{"content":"就是相处的一部分吧。对"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 11 字符，总长度: 234
接收到原始数据 (275 字节): data: {"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qw...
处理接收数据: 'data: {"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 265)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 265)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"了，你今天\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 256)
完整JSON字符串: '{"choices":[{"delta":{"content":"了，你今天"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
添加内容到打字机队列: 5 字符，总长度: 239
接收到原始数据 (278 字节): data: {"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"q...
处理接收数据: 'data: {"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 266)
当前缓冲区内容: 'data: {"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 266)
接收到原始数据 (262 字节): data: {"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-...
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"delta\":{\"content\":\"过得怎么样？\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
检测到完整JSON，立即处理: '{"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 257)
完整JSON字符串: '{"choices":[{"delta":{"content":"过得怎么样？"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
处理接收数据: 'data: {"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (长度: 262)
当前缓冲区内容: 'data: {"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}


' (总长度: 262)
使用标准SSE格式处理
分割后行数: 4
保留最后一行: ''
完整行数: 1
开始处理完整行: ["data: {\"choices\":[{\"finish_reason\":\"stop\",\"delta\":{\"content\":\"\"},\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754031609,\"system_fingerprint\":null,\"model\":\"qwen-plus\",\"id\":\"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f\"}"]
处理SSE行，总行数: 1
处理第1行: 'data: {"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
解析到data字段: '{"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
添加内容到打字机队列: 6 字符，总长度: 245
检测到完整JSON，立即处理: '{"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}'
开始处理事件数据: '{"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (长度: 253)
完整JSON字符串: '{"choices":[{"finish_reason":"stop","delta":{"content":""},"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754031609,"system_fingerprint":null,"model":"qwen-plus","id":"chatcmpl-e28d1658-777e-90f5-85c4-87893aff5a1f"}' (缓冲区长度: 0)
尝试解析JSON数据...
JSON解析成功，触发onEventReceived回调
断开SSE连接
SSE连接已关闭
SSE连接被取消
-[AFPreferences _languageCodeWithFallback:] No language code saved, but Assistant is enabled - returning: zh-CN
AX Lookup problem - errorCode:1100 error:Permission denied portName:'com.apple.iphone.axserver' PID:19767 (
	0   AXRuntime                           0x00000001beece0ac _AXGetPortFromCache + 796
	1   AXRuntime                           0x00000001beed2b70 AXUIElementPerformFencedActionWithValue + 700
	2   UIKit                               0x000000024332741c 5BCB8FCD-7A8D-36EE-ADE7-36BE0469FEA4 + 1561628
	3   libdispatch.dylib                   0x0000000104c04584 _dispatch_call_block_and_release + 32
	4   libdispatch.dylib                   0x0000000104c1e064 _dispatch_client_callout + 16
	5   libdispatch.dylib                   0x0000000104c0c91c _dispatch_lane_serial_drain + 796
	6   libdispatch.dylib                   0x0000000104c0d5a4 _dispatch_lane_invoke + 432
	7   libdispatch.dylib                   0x0000000104c19894 _dispatch_root_queue_drain_deferred_wlh + 344
	8   libdispatch.dylib                   0x0000000104c18eb0 _dispatch_workloop_worker_thread + 580
	9   libsystem_pthread.dylib             0x0000000211598a0c _pthread_wqthread + 292
	10  libsystem_pthread.dylib             0x0000000211598aac start_wqthread + 8
)
-[RTIInputSystemClient remoteTextInputSessionWithID:performInputOperation:]  perform input operation requires a valid sessionID. inputModality = Keyboard, inputOperation = dismissAutoFillPanel, customInfoType = UIUserInteractionRemoteInputOperations
warning: (arm64) /Users/<USER>/Library/Developer/Xcode/DerivedData/Seeyou-ginbxelbpsktdzcevblbigjuzprt/Build/Products/Debug-iphoneos/Seeyou.app/Seeyou empty dSYM file detected, dSYM was created with an executable with no debug info.
性能分析报告:
性能指标报告:
- 平均会话切换时间: 0.000秒
- 缓存命中率: 57.1%
- 错误率: 0.0%
- 总操作数: 7