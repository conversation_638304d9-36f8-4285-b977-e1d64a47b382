//
//  ChatViewModel.swift
//
//
//  Created by zwt on 2024/4/8.
//  Updated by AI Assistant on 2025/01/29.
//

import AVFoundation
import Combine
import Foundation
import OSLog
import SwifterSwift
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatViewModel")

class ChatViewModel: NSObject, ObservableObject {
    private let databaseQueue = DispatchQueue(label: "com.sanva.chatadvisor.viewModel", qos: .background)
    
    private let equalId = UUID().uuidString
    static var allModels: [ChatsModel] = []
    @ObservedObject var stepFormViewModel: MultiStepFormViewModel
    @Published var currentChat: Chat
    @Published var lastScrollTime = Date()
    @Published var lastLoadTime = Date()
    @Published var needScrollToBottom = false
    @Published var isRecording: Bool = false
    @Published var userIsScrolling = false
    @Published var iOS17DefaultScrollAnchor: UnitPoint = .top
    @Published var questions: [Question] = BootManager.shared.questions
    @Published var allChatModels: [ChatsModel] = ChatViewModel.allModels {
        didSet {
            ChatViewModel.allModels = allChatModels
        }
    }
    
    @Published var currentModel: ChatsModel = .default
    @Published var textInput = ""
    @Published var readChatText = ""
    // 保留一些必要的状态属性，其他委托给UIStateManager
    @Published var isLoadingPricing = false
    @Published var isLoadingMessage = false
    @Published var isPreloading = false
    @Published var isFirstSend = true
    @Published var isRequestError = false
    
    // UI状态委托给UIStateManager
    var isAnswering: Bool { uiStateManager.isAnswering }
    var isLoadingInitialMessages: Bool { uiStateManager.isLoadingInitialMessages }
    var showTypingIndicator: Bool { uiStateManager.showTypingIndicator }
    var showToast: Bool { uiStateManager.showToast }
    var toastMessage: String { uiStateManager.toastMessage }
    var selectedMessage: ChatMessage? { uiStateManager.selectedMessage }
    var aiTypingState: AITypingState { uiStateManager.aiTypingState }
    
    // 新增：智能滚动管理器
    @Published var scrollManager = SmartScrollManager()
    
    let scrollThrottleInterval = 0.15
    let loadingThrottleInterval = 1.0
    let uiUpdateThrottleInterval = 0.1
    
    private var lastMessageCount = 0
    private var lastContentUpdateTime = Date()
    private var contentUpdateWorkItem: DispatchWorkItem?
    
    weak var chatListViewModel: ChatListViewModel?
    private var currentRole: Role = .assistant
    private var currentStreamingMessageId: String?
    private var currentLocalResponseId: String? // 本地生成的响应ID，用于管理消息流
    
    private lazy var chatManager = UnifiedSSEManager<ChatCompletionChunk>()
    private lazy var typewriterManager = TypewriterEffectManager() // 打字机效果管理器
    
    // 新增：重构后的组件
    private lazy var messageManager: MessageManager = {
        let chatRepo = ChatRepository(
            database: AdvisorDatabaseManager.shared.database!,
            databaseQueue: AdvisorDatabaseManager.shared.databaseQueue,
            cacheManager: try! CacheManager<Chat>(name: "chats")
        )
        let messageRepo = MessageRepository(
            database: AdvisorDatabaseManager.shared.database!,
            databaseQueue: AdvisorDatabaseManager.shared.databaseQueue,
            cacheManager: try! CacheManager<ChatMessage>(name: "messages")
        )
        return MessageManager(
            chatRepository: chatRepo,
            messageRepository: messageRepo,
            cacheManager: try! CacheManager<ChatMessage>(name: "messages")
        )
    }()
    
    private lazy var uiStateManager = ChatUIStateManager()
    
    /// 获取当前流式消息的显示内容（用于UI显示）
    func getStreamingMessageDisplayContent(for messageId: String) -> String {
        // 如果是当前流式消息且打字机效果正在运行或已完成但显示未完成
        if messageId == currentLocalResponseId {
            switch typewriterManager.state {
            case .idle:
                // 打字机空闲，返回消息实际内容
                break
            case .typing, .paused:
                // 打字机正在运行，返回当前显示内容
                return typewriterManager.displayedContent
            case .completed:
                // 打字机已完成，检查消息显示状态
                if let message = currentChat.messages.first(where: { $0.id == messageId }),
                   !message.isDisplayComplete {
                    // 显示未完成，返回打字机的完整内容
                    return typewriterManager.getFullContent
                }
                // 显示已完成，返回消息实际内容
                break
            }
        }

        // 返回消息的实际内容
        return currentChat.messages.first(where: { $0.id == messageId })?.content ?? ""
    }

    /// 标记消息显示完成
    private func markMessageDisplayComplete(messageId: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                logger.warning("markMessageDisplayComplete: self已释放")
                return
            }

            if let messageIndex = self.currentChat.messages.firstIndex(where: { $0.id == messageId }) {
                var updatedMessage = self.currentChat.messages[messageIndex]

                // 关键修复：将打字机效果的完整内容同步到消息对象
                if messageId == self.currentLocalResponseId {
                    let typewriterContent = self.typewriterManager.getFullContent
                    if !typewriterContent.isEmpty && typewriterContent != updatedMessage.content {
                        updatedMessage.content = typewriterContent
                        logger.info("同步打字机内容到消息: \(typewriterContent.count) 字符")
                    }
                }

                updatedMessage.isDisplayComplete = true
                self.currentChat.messages[messageIndex] = updatedMessage

                logger.info("消息显示完成: \(messageId), 内容长度: \(updatedMessage.content.count)")

                // 验证内容完整性
                if messageId == self.currentLocalResponseId {
                    let typewriterContentLength = self.typewriterManager.getFullContent.count
                    let messageContentLength = updatedMessage.content.count
                    if typewriterContentLength != messageContentLength {
                        logger.warning("内容长度不匹配 - 打字机: \(typewriterContentLength), 消息: \(messageContentLength)")
                    } else {
                        logger.info("内容同步验证通过: \(messageContentLength) 字符")
                    }
                }

                // 异步保存到数据库，确保内容完整性
                Task {
                    await AdvisorDatabaseManager.shared.update(message: updatedMessage)
                }
            } else {
                logger.warning("markMessageDisplayComplete: 未找到消息 \(messageId)")
            }
        }
    }
    
    private let processQueue = DispatchQueue(label: "com.sanva.processChatQueue")
    // 新增：消息处理串行队列，确保SSE消息更新的顺序性
    private let messageUpdateQueue = DispatchQueue(label: "com.sanva.messageUpdateQueue", qos: .userInitiated)
    private lazy var synthesizer = AVSpeechSynthesizer()
    private var cancellables = Set<AnyCancellable>()
    
    // 新增：超时管理
    private var requestTimeoutTimer: Timer?
    private let requestTimeoutInterval: TimeInterval = 60.0 // 60秒超时
    
    init(chatListViewModel: ChatListViewModel?) {
        let currentChat = AdvisorDatabaseManager.shared.startNewChat()
        let stepFormViewModel = ChatConfigDatabaseManager.shared.newForm(chatId: currentChat.id)
        self.stepFormViewModel = stepFormViewModel
        self.currentChat = currentChat
        self.chatListViewModel = chatListViewModel
        super.init()
        
        setCurrentChat(chat: currentChat)
        setupBindings()
    }
    
    private func setupBindings() {
        // 绑定stepFormViewModel变化
        stepFormViewModel.objectWillChange
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        // 绑定UI状态管理器
        uiStateManager.objectWillChange
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        // 绑定消息管理器
        messageManager.objectWillChange
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
    
    deinit {
        // 清理所有资源
        stopRequestTimeoutTimer()
        chatManager.disconnect()
        cancellables.forEach { $0.cancel() }
        contentUpdateWorkItem?.cancel()
    }
    
    // MARK: - UI状态管理便利方法
    
    func updateAITypingState(_ state: AITypingState) {
        uiStateManager.updateAITypingState(state)
    }
    
    func showSuccessToast(_ message: String) {
        uiStateManager.showSuccessToast(message)
    }
    
    func showErrorToast(_ message: String) {
        uiStateManager.showErrorToast(message)
    }
    
    func showWarningToast(_ message: String) {
        uiStateManager.showWarningToast(message)
    }
    
    func selectMessage(_ message: ChatMessage?) {
        uiStateManager.selectMessage(message)
    }
    
    func loadMoreIfNeeds() {
        // 防止重复加载
        guard !isLoadingMessage && !isPreloading else { return }
        
        // 检查是否需要加载更多消息
        guard let firstMessage = currentChat.messages.first else { return }
        
        // 使用时间间隔来防抖
        let now = Date()
        guard now.timeIntervalSince(lastLoadTime) > loadingThrottleInterval else { return }
        lastLoadTime = now
        
        // 检查是否到达顶部或需要预加载
        let thresholdIndex = currentChat.messages.startIndex
        let shouldLoad = currentChat.messages.firstIndex(where: { $0.id == firstMessage.id }) == thresholdIndex || scrollManager.shouldTriggerPreload
        
        if shouldLoad {
            Task {
                await loadOlderMessages(isPreload: scrollManager.shouldTriggerPreload)
            }
        }
    }
    
    func readAloud(text: String) {
        readChatText = text
        let utterance = AVSpeechUtterance(string: readChatText)
        utterance.voice = AVSpeechSynthesisVoice(language: readChatText.isContainChinese ? "zh-CN" : "en-US")
        
        synthesizer.speak(utterance)
    }
    
    func fetchCurrentChatMessages() async {
        guard !isLoadingMessage else { return }
        isLoadingMessage = true
        uiStateManager.startLoadingInitialMessages()  // 标记为初始消息加载
        let messages = await AdvisorDatabaseManager.shared.fetchMessages(chatId: currentChat.id)
        // 将更新操作调度到主线程以更新UI
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            currentChat.messages = messages
            isLoadingMessage = false
            uiStateManager.finishLoadingInitialMessages()  // 重置初始加载标志
        }
    }
    
    func loadOlderMessages(isPreload: Bool = false) async {
        // 根据是否为预加载设置不同的状态
        if isPreload {
            guard !isPreloading && !isLoadingMessage else { return }
            isPreloading = true
        } else {
            guard !isLoadingMessage else { return }
            isLoadingMessage = true
        }
        
        // 使用优化的分页查询
        let beforeTimestamp = currentChat.messages.first?.createdTime
        let limit = isPreload ? 10 : 20 // 预加载时加载较少消息
        let olderMessages = await AdvisorDatabaseManager.shared.fetchMessagesPaginated(
            chatId: currentChat.id,
            beforeTimestamp: beforeTimestamp,
            limit: limit
        )
        
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            
            // 增强的消息去重逻辑：基于ID和时间戳双重验证
            let newMessages = olderMessages.filter { oldMessage in
                // 检查ID是否重复
                let idExists = self.currentChat.messages.contains { $0.id == oldMessage.id }
                
                // 检查是否有相同时间戳和内容的消息（防止数据库重复）
                let duplicateExists = self.currentChat.messages.contains { existingMessage in
                    existingMessage.createdTime == oldMessage.createdTime &&
                    existingMessage.content == oldMessage.content &&
                    existingMessage.role == oldMessage.role
                }
                
                return !idExists && !duplicateExists
            }
            
            if !newMessages.isEmpty {
                // 按时间戳排序，确保消息顺序正确
                let sortedNewMessages = newMessages.sorted { $0.createdTime < $1.createdTime }
                self.currentChat.messages.insert(contentsOf: sortedNewMessages, at: 0)
                logger.info("\(isPreload ? "预加载" : "加载")了 \(newMessages.count) 条历史消息")
                
                // 验证消息完整性
                //                self.validateMessageIntegrity()
            }
            
            // 重置加载状态
            if isPreload {
                isPreloading = false
            } else {
                isLoadingMessage = false
            }
        }
    }
    
    func startNewChat() {
        textInput = ""
        isFirstSend = true
        userIsScrolling = false
        iOS17DefaultScrollAnchor = .top
        currentChat = AdvisorDatabaseManager.shared.startNewChat()
        // 确保新建会话时消息数组为空
        currentChat.messages.removeAll()
        currentChat.title = stepFormViewModel.title
        stepFormViewModel.chatId = currentChat.id
        currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
    }
    
    func setCurrentChat(chat: Chat) {
        isFirstSend = false
        
        // 记录之前的会话ID用于比较
        let previousChatId = currentChat.id
        
        // 如果是同一个会话，避免重复设置
        if previousChatId == chat.id {
            // 只更新标题和表单配置，不重新加载消息
            stepFormViewModel.chatId = chat.id
            currentChat.title = stepFormViewModel.title
            stepFormViewModel.loadFromDatabase()
            return
        }
        
        // 重置UI状态
        uiStateManager.resetUIState()
        
        // 更新当前会话的基本信息
        currentChat = chat
        iOS17DefaultScrollAnchor = .bottom
        stepFormViewModel.chatId = chat.id
        currentChat.title = stepFormViewModel.title
        
        // 使用MessageManager加载消息
        Task { @MainActor in
            uiStateManager.startLoadingInitialMessages()
            
            // 设置MessageManager的聊天ID并加载消息
            await messageManager.setChatId(chat.id)
            
            // 同步消息到currentChat
            currentChat.messages = messageManager.messages
            
            // 加载表单配置
            stepFormViewModel.loadFromDatabase()
            
            // 验证消息完整性
            validateAndFixIncompleteMessages()
            
            // 只有当会话中已有用户消息时才导入提示词
            if currentChat.messages.contains(where: { $0.role == .user }) {
                importPrompt()
            }
            
            isLoadingMessage = false
            uiStateManager.finishLoadingInitialMessages()  // 重置初始加载标志
        }
    }
    
    
    /// 验证并修复未完成的消息
    private func validateAndFixIncompleteMessages() {
        for (index, message) in currentChat.messages.enumerated() {
            // 检查AI消息是否标记为未完成但实际已完成
            if message.role == .assistant && !message.isComplete && !message.content.isEmpty {
                var fixedMessage = message
                fixedMessage.isComplete = true
                currentChat.messages[index] = fixedMessage
                
                // 异步更新数据库
                Task {
                    await AdvisorDatabaseManager.shared.update(message: fixedMessage)
                }
            }
        }
    }
    
    /// 验证消息完整性
    private func validateMessageIntegrity() {
        // 检查消息时间戳顺序
        for i in 1..<currentChat.messages.count {
            let prevMessage = currentChat.messages[i-1]
            let currentMessage = currentChat.messages[i]
            
            if prevMessage.createdTime > currentMessage.createdTime {
                logger.warning("发现消息时间戳顺序异常: \(prevMessage.id) -> \(currentMessage.id)")
            }
        }
        
        // 检查是否有重复的消息ID
        let messageIds = currentChat.messages.map { $0.id }
        let uniqueIds = Set(messageIds)
        if messageIds.count != uniqueIds.count {
            logger.warning("发现重复的消息ID，消息总数: \(messageIds.count)，唯一ID数: \(uniqueIds.count)")
        }
    }
    
    func importPrompt() {
        currentChat.messages.removeAll { $0.role == .system }
        let promptContent = stepFormViewModel.generatePrompt()
        // 只有当提示词内容不为空时才插入系统消息
        if !promptContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            let chatMessage = ChatMessage(id: UUID().uuidString, chatId: currentChat.id, role: .system, content: promptContent, isComplete: true, messageType: .text, assetURL: nil)
            currentChat.messages.insert(chatMessage, at: 0)
        }
        stepFormViewModel.isProcessing = false
    }
    
    func sendMessage() {
        guard AccountManager.shared.currentUser != nil else {
            AccountManager.shared.needLoggedIn = true
            return
        }
        guard !textInput.isEmpty else { return }
        sendMessage(textInput)
        textInput = ""
    }
    
    func sendRecording(assetURL: URL) {
        uiStateManager.updateAITypingState(.connecting)
        // isAnswering 和 showTypingIndicator 通过 updateAITypingState 自动设置
        NetworkService.shared.requestMulti(ChatTarget.uploadAudio(assetURL: assetURL)) { [weak self] (result: Result<NetworkResponse<String>, NetworkError>) in
            guard let self else { return }
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                switch result {
                case let .success(response):
                    if response.isSuccess {
                        sendMessage(response.data ?? "", messageType: .audio, assetURL: assetURL)
                    } else {
                        failedToast(response.message ?? "语音转换失败".localized())
                    }
                case let .failure(error):
                    failedToast(error.localizedDescription)
                }
                uiStateManager.updateAITypingState(.idle)
            }
        }
    }
    
    func saveCurrentChat() {
        currentChat.title = stepFormViewModel.title
        
        // 同步保存到数据库，确保后续操作能立即访问
        AdvisorDatabaseManager.shared.update(chat: currentChat)
        AdvisorDatabaseManager.shared.update(messages: currentChat.messages)
        
        logger.info("同步保存当前会话: chatId=\(self.currentChat.id), title=\(self.currentChat.title)")
        
        // 异步更新内存列表
        Task { @MainActor in
            // 立即更新内存中的会话列表
            chatListViewModel?.updateMemoryChat(newChat: currentChat)
            
            logger.info("会话列表已更新: chatId=\(self.currentChat.id)")
        }
    }
    
    func sendMessage(_ content: String, messageType: MessageType = .text, assetURL: URL? = nil) {
        // 防止重复发送：如果当前正在处理请求，则忽略新的发送请求
        guard !uiStateManager.isAnswering else {
            logger.warning("正在处理请求中，忽略新的发送请求")
            return
        }
        
        // 用户消息应该立即标记为完成状态
        let message = ChatMessage(id: UUID().uuidString, chatId: currentChat.id, role: .user, content: content, isComplete: true, messageType: messageType, assetURL: assetURL)
        
        // 第一次发送消息时，先导入提示词（如果需要）
        if isFirstSend {
            importPrompt()
        }
        
        // 使用MessageManager添加消息
        Task { @MainActor in
            await messageManager.addMessage(message)
            
            // 同步到currentChat
            currentChat.messages = messageManager.messages
            
            // 第一次发送消息时才存储对话和更新列表
            if isFirstSend {
                // 确保原子性操作：先存储到数据库，再更新内存列表
                AdvisorDatabaseManager.shared.update(chat: currentChat)
                
                // 立即更新内存中的会话列表（此时会话已包含用户消息）
                chatListViewModel?.updateMemoryChat(newChat: currentChat)
                
                logger.info("首次发送消息，已更新会话列表: chatId=\(self.currentChat.id)")
                isFirstSend = false
            }
            
            // 发送消息
            postMessages(forChat: currentChat)
        }
    }
    
    func postMessages(forChat chat: Chat? = nil) {
        // 防止重复发送：如果当前正在处理请求，则忽略新的发送请求
        guard !isAnswering else {
            logger.warning("正在处理请求中，忽略新的postMessages请求")
            return
        }
        
        // 导入后直接发送也需要保存
        if AdvisorDatabaseManager.shared.fetchChatSync(id: currentChat.id) == nil {
            // 确保原子性操作：先存储到数据库，再更新内存列表
            Task { @MainActor in
                // 先存储到数据库
                AdvisorDatabaseManager.shared.update(chat: currentChat)
                
                // 立即更新内存中的会话列表
                chatListViewModel?.updateMemoryChat(newChat: currentChat)
                
                logger.info("postMessages保存新会话并更新列表: chatId=\(self.currentChat.id)")
            }
        }
        let chat = chat ?? currentChat
        FirebaseManager.shared.logChat(alias: "local")
        let messages = chat.messages
        uiStateManager.updateAITypingState(.connecting)

        // 重置流消息状态和打字机效果
        currentStreamingMessageId = nil
        currentLocalResponseId = UUID().uuidString // 生成本地响应ID
        typewriterManager.reset() // 开始新请求时重置打字机效果
        
        // 启动超时计时器
        startRequestTimeoutTimer()
        chatManager.onEventReceived = { [weak self] chunk in
            guard let self else { return }
            if Preferences.feedbackEnabled.value {
                VibrationController.shared.triggerImpactFeedback(style: .soft)
            }
            // 新增：收到第一个数据块时，更新AI状态为输入中
            if aiTypingState == .connecting || aiTypingState == .thinking {
                DispatchQueue.main.async {
                    self.uiStateManager.updateAITypingState(.typing)
                }
            }
            processChatResponse(chunk: chunk)
        }
        
        // 新增：错误处理回调
        chatManager.onError = { [weak self] error in
            guard let self else { return }
            DispatchQueue.main.async {
                self.handleSSEError(error)
            }
        }
        
        // 新增：连接状态回调
        chatManager.onConnectionClosed = { [weak self] in
            guard let self else { return }
            DispatchQueue.main.async {
                self.handleConnectionClosed()
            }
        }
        
        // 连接成功回调
        chatManager.onConnectionOpened = { [weak self] in
            guard let self else { return }
            DispatchQueue.main.async {
                self.uiStateManager.updateAITypingState(.thinking)
                // 连接成功后立即隐藏网络状态指示器
                NetworkStatusManager.shared.hideNetworkStatus()
            }
            logger.info("SSE连接已建立")
        }
        
        // 连接关闭回调
        chatManager.onConnectionClosed = { [weak self] in
            guard let self else { return }
            DispatchQueue.main.async {
                self.stopRequestTimeoutTimer() // 停止超时计时器
                self.uiStateManager.updateAITypingState(.idle)
                self.currentStreamingMessageId = nil
                self.currentLocalResponseId = nil // 清理本地响应ID
            }
            logger.info("SSE连接已关闭")
        }
        
        // 错误处理回调
        chatManager.onError = { error in
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                self.stopRequestTimeoutTimer() // 停止超时计时器
                self.uiStateManager.updateAITypingState(.idle)
                self.currentStreamingMessageId = nil
                self.currentLocalResponseId = nil // 清理本地响应ID
                self.failedToast(error.localizedDescription)
            }
            logger.error("Error received: \(error.localizedDescription)")
        }
        
        var filteredMessages: [ChatMessage] = []
        filteredMessages = messages.map { message in
            if message.role == .assistant {
                message.copyWith(content: message.content.removingMarkdown())
            } else {
                message
            }
        }
        // 移除system
        filteredMessages.removeAll { $0.role == .system || $0.finishReason == .noBalance }
        // 如果message的所有content的长度大于thresholdValue，减少最远的message,直到总长度小于thresholdValue
        var totalLength = 0
        let thresholdValue = currentModel.count * 2
        var messagesToSend: [ChatMessage] = []
        for message in filteredMessages.reversed() where totalLength < thresholdValue {
            totalLength += message.content.count
            messagesToSend.insert(message, at: 0)
        }
        
        // 插入提示词
        let chatMessage = ChatMessage(id: UUID().uuidString, chatId: currentChat.id, role: .system, content: stepFormViewModel.generatePrompt(), isComplete: true, messageType: .text, assetURL: nil)
        messagesToSend.insert(chatMessage, at: 0)
        
        // 包装消息数组为正确的请求格式
        let requestBody = ["messages": messagesToSend]
        let httpBody = try? JSONEncoder().encode(requestBody)
        var header = APIBase.commonHeaders()
        header["modelname"] = currentModel.modelName
        header["chatid"] = currentChat.id
        header["Accept"] = "text/event-stream"
        header["Content-Type"] = "application/json"
        if let body = httpBody {
            if let signature = CryptoFunction.createSignature(data: body) {
                header["signature"] = signature
            }
        }
        chatManager.connect(url: NetworkURL.chat, method: "POST", httpBody: httpBody, headers: header)
    }
    
    func postMessages(_ message: PostMessage) {
        // 防止重复发送：如果当前正在处理请求，则忽略新的发送请求
        guard !isAnswering else {
            logger.warning("正在处理请求中，忽略新的postMessages请求")
            return
        }
        
        uiStateManager.updateAITypingState(.connecting)

        // 重置流消息状态和打字机效果
        currentStreamingMessageId = nil
        currentLocalResponseId = UUID().uuidString // 生成本地响应ID
        typewriterManager.reset() // 开始新请求时重置打字机效果
        
        // 启动超时计时器
        startRequestTimeoutTimer()
        // 导入后直接发送也需要保存
        if AdvisorDatabaseManager.shared.fetchChatSync(id: currentChat.id) == nil {
            // 确保原子性操作：先存储到数据库，再更新内存列表
            Task { @MainActor in
                // 先存储到数据库
                AdvisorDatabaseManager.shared.update(chat: currentChat)
                
                // 立即更新内存中的会话列表（只有包含用户消息的会话才会被添加）
                chatListViewModel?.updateMemoryChat(newChat: currentChat)
                
                logger.info("导入消息后更新会话列表: chatId=\(self.currentChat.id)")
            }
        }
        FirebaseManager.shared.logChat(alias: "remote")
        var header = APIBase.commonHeaders()
        header["modelname"] = currentModel.modelName
        header["chatid"] = currentChat.id
        header["Accept"] = "text/event-stream"
        header["Content-Type"] = "application/json"
        header["remote-recognize"] = "1"
        let messageBody = PostMessageBody(messages: [
            //            .chatMessage(ChatMessage(id: UUID().uuidString, chatId: currentChat.id, role: .system, content: stepFormViewModel.generatePrompt(), isComplete: true, messageType: .text, assetURL: nil)),
            .postMessage(message),
        ])
        let httpBody = try? JSONEncoder().encode(messageBody)
        if let body = httpBody {
            if let signature = CryptoFunction.createSignature(data: body) {
                header["signature"] = signature
            }
        }
        
        chatManager.connect(url: NetworkURL.chat, method: "POST", httpBody: httpBody, headers: header)
    }
    
    func processChatResponse(chunk: ChatCompletionChunk) {
        DispatchQueue.main.async {
            // showTypingIndicator 通过 updateAITypingState 管理，这里不需要单独设置
        }
        
        // 处理错误情况
        if chunk.choices.first?.finishReason == .error {
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                failedToast(chunk.choices.first?.delta?.content ?? "服务器内部错误".localized())
                uiStateManager.updateAITypingState(.idle)
                currentStreamingMessageId = nil
                currentLocalResponseId = nil // 清理本地响应ID
            }
            return
        }
        
        // 处理流结束
        if chunk.choices.first?.finishReason == .stop {
            chatManager.disconnect()
            finishCurrentMessage(chunk: chunk)
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                stopRequestTimeoutTimer() // 停止超时计时器
                uiStateManager.updateAITypingState(.idle)
                currentStreamingMessageId = nil
                currentLocalResponseId = nil // 清理本地响应ID
                // 标记有新消息，用于智能滚动
                scrollManager.markNewMessage()
            }
            return
        }
        
        // 处理流数据
        processStreamingChunk(chunk)
    }
    
    private func processStreamingChunk(_ chunk: ChatCompletionChunk) {
        // 检查是否已有当前响应的消息
        guard let localResponseId = currentLocalResponseId else { return }

        // 使用串行队列确保消息处理的原子性
        messageUpdateQueue.async { [weak self] in
            guard let self = self else { return }

            // 在串行队列中查找消息，确保线程安全
            let existingMessageIndex = self.currentChat.messages.firstIndex { $0.id == localResponseId }

            if existingMessageIndex == nil {
                // 如果没有现有消息，尝试创建新消息（只有包含内容时才创建）
                self.createNewStreamingMessage(chunk: chunk)
            } else {
                // 追加内容到现有消息
                self.appendContentToCurrentMessage(chunk: chunk)
            }
        }
    }
    
    private func createNewStreamingMessage(chunk: ChatCompletionChunk) {
        // 获取内容
        let content = chunk.choices.compactMap { $0.delta?.content }.joined()

        // 只有当包含实际内容时才创建消息，避免空气泡
        guard !content.isEmpty else { return }

        // 使用本地响应ID作为消息ID
        guard let localResponseId = currentLocalResponseId else { return }

        // 在当前队列中再次检查，防止重复创建
        if currentChat.messages.contains(where: { $0.id == localResponseId }) {
            logger.debug("消息已存在，跳过创建: \(localResponseId)")
            return
        }

        // 确定消息角色
        if let delta = chunk.choices.first?.delta, let roleString = delta.role {
            currentRole = (roleString == "assistant") ? .assistant : .user
        }

        let newMessage = ChatMessage(
            id: localResponseId, // 使用本地响应ID
            chatId: currentChat.id,
            role: currentRole,
            content: "", // 初始内容为空，通过打字机效果显示
            isComplete: false,
            isDisplayComplete: false, // 显示未完成
            finishReason: chunk.choices.first?.finishReason
        )
        
        // 在主线程安全地添加消息
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }

            // 再次检查是否已存在，防止并发添加
            if !self.currentChat.messages.contains(where: { $0.id == localResponseId }) {
                self.currentChat.messages.append(newMessage)

                // 确保打字机效果使用正确的消息ID
                // 只有在开始新的流式消息时才重置打字机效果
                if self.currentStreamingMessageId != localResponseId {
                    self.typewriterManager.reset()
                    self.currentStreamingMessageId = localResponseId

                    // 设置打字机效果完成回调
                    self.typewriterManager.onTypingCompleted = { [weak self] in
                        self?.markMessageDisplayComplete(messageId: localResponseId)
                    }
                }

                // 添加内容到打字机效果
                if !content.isEmpty {
                    self.typewriterManager.appendContent(content)
                }

                // 异步保存到数据库
                Task {
                    if chunk.choices.first?.finishReason != .noBalance {
                        await AdvisorDatabaseManager.shared.update(message: newMessage)
                    }
                }
            }
        }
    }
    
    private func appendContentToCurrentMessage(chunk: ChatCompletionChunk) {
        // 使用本地响应ID查找消息
        guard let localResponseId = currentLocalResponseId else {
            logger.warning("appendContentToCurrentMessage: currentLocalResponseId为空")
            return
        }

        // 在当前串行队列中查找消息索引
        guard let messageIndex = self.currentChat.messages.firstIndex(where: { $0.id == localResponseId }) else {
            // 如果找不到消息，记录错误但不创建新消息，避免重复创建
            logger.warning("未找到消息ID: \(localResponseId)，跳过内容追加")
            return
        }

        let contentToAdd = chunk.choices.compactMap { $0.delta?.content }.joined()

        // 确保内容不为空才进行更新
        guard !contentToAdd.isEmpty else { return }

        // 在主线程更新UI和打字机效果
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 再次验证索引有效性（防止并发修改）
            guard messageIndex < self.currentChat.messages.count,
                  self.currentChat.messages[messageIndex].id == localResponseId else {
                logger.warning("消息索引无效或ID不匹配")
                return
            }

            // 将新内容添加到打字机效果管理器
            self.typewriterManager.appendContent(contentToAdd)

            // 更新消息的实际内容（用于数据库存储）
            var updatedMessage = self.currentChat.messages[messageIndex]
            updatedMessage.append(content: contentToAdd)
            self.currentChat.messages[messageIndex] = updatedMessage

            // 异步保存到数据库（在UI更新后执行）
            Task {
                if chunk.choices.first?.finishReason != .noBalance {
                    await AdvisorDatabaseManager.shared.update(message: updatedMessage)
                }
            }
        }
    }
    
    private func finishCurrentMessage(chunk: ChatCompletionChunk) {
        // 使用本地响应ID查找消息
        guard let localResponseId = currentLocalResponseId else {
            logger.warning("finishCurrentMessage: currentLocalResponseId为空")
            return
        }

        // 使用串行队列确保消息完成状态设置的原子性
        messageUpdateQueue.async { [weak self] in
            guard let self = self else { return }

            guard let messageIndex = self.currentChat.messages.firstIndex(where: { $0.id == localResponseId }) else {
                logger.warning("finishCurrentMessage: 未找到消息ID: \(localResponseId)")
                return
            }
            
            let contentToAdd = chunk.choices.compactMap { $0.delta?.content }.joined()
            let finishReason = chunk.choices.first?.finishReason
            
            // 在主线程更新UI和消息状态
            DispatchQueue.main.async {
                // 再次验证索引有效性
                guard messageIndex < self.currentChat.messages.count,
                      self.currentChat.messages[messageIndex].id == localResponseId else { return }
                
                var updatedMessage = self.currentChat.messages[messageIndex]
                
                // 只有当内容不为空时才追加
                if !contentToAdd.isEmpty {
                    updatedMessage.append(content: contentToAdd)
                    // 将最后的内容也添加到打字机效果
                    self.typewriterManager.appendContent(contentToAdd)
                }
                
                // 设置消息完成状态（数据完整，但显示可能未完成）
                updatedMessage.isComplete = true
                updatedMessage.finishReason = finishReason
                self.currentChat.messages[messageIndex] = updatedMessage

                // 让打字机效果自然完成，不强制立即完成
                // 显示完成状态将在打字机效果完成时通过回调设置

                // 安全机制：如果打字机效果已经完成但消息显示未完成，立即标记完成
                if self.typewriterManager.state == .completed && !updatedMessage.isDisplayComplete {
                    self.markMessageDisplayComplete(messageId: localResponseId)
                }
                
                // 异步保存到数据库，确保消息完整性
                Task {
                    if finishReason != .noBalance {
                        await AdvisorDatabaseManager.shared.update(message: updatedMessage)
                    }
                }
            }
        }
    }
    
    // MARK: - 错误处理和连接管理
    
    /// 处理SSE连接错误
    private func handleSSEError(_ error: Error) {
        logger.error("SSE连接错误: \(error.localizedDescription)")
        
        // 停止超时计时器
        stopRequestTimeoutTimer()
        
        // 重置状态
        uiStateManager.updateAITypingState(.idle)
        
        // 完成当前打字机效果（如果有的话）
        typewriterManager.completeImmediately()
        
        // 显示错误提示
        failedToast("连接中断，请重试")
        
        // 清理当前流消息状态
        currentStreamingMessageId = nil
        currentLocalResponseId = nil
    }
    
    /// 处理连接关闭
    private func handleConnectionClosed() {
        logger.info("SSE连接已关闭")
        
        // 如果正在回答中，说明连接异常关闭
        if isAnswering {
            handleSSEError(NSError(domain: "ChatViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "连接意外关闭"]))
        }
    }
    
    /// 重试当前请求
    func retryCurrentRequest() {
        guard !isAnswering else { return }
        
        // 如果有未完成的消息，重新发送
        if let lastUserMessage = currentChat.messages.last(where: { $0.role == .user }) {
            logger.info("重试发送消息")
            
            // 移除可能存在的不完整助手消息
            currentChat.messages.removeAll { message in
                message.role == .assistant && !message.isComplete
            }
            
            // 重新发送消息
            postMessages(forChat: currentChat)
        }
    }
}

extension ChatViewModel {
    func sendrecor() {
        // 这里放录音开始的代码
        isRecording = true
        // 开始录音逻辑
    }

    func stopRecording() {
        // 这里放录音结束的代码
        isRecording = false
        // 停止录音逻辑
    }
}

extension ChatViewModel {
    func getPricing() {
        guard !isLoadingPricing else { return }
        isLoadingPricing = true
        NetworkService.shared.requestMulti(PricingTarget.getPricing) { [weak self] (result: Result<NetworkResponse<[ChatsModel]>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    if response.isSuccess {
                        allChatModels = response.data ?? BootManager.shared.allChatModels
                        ChatViewModel.allModels = response.data ?? BootManager.shared.allChatModels
                    } else {
                        allChatModels = []
                    }
                    currentModel = allChatModels.first ?? ChatsModel.default
                    isLoadingPricing = false
                }
//                logger.info("getPricing successful: \(response.localizedDescription)")
            case let .failure(error):
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoadingPricing = false
                }
                logger.error("getPricing failed: \(error.localizedDescription)")
            }
        }
    }
}

extension ChatViewModel {
    func failedToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            // 重置所有相关状态，确保下次对话能正常进行
            uiStateManager.updateAITypingState(.idle)
            currentStreamingMessageId = nil
            currentLocalResponseId = nil
            // 注意：不重置isLoadingInitialMessages，因为它只用于初始加载

            // 设置错误提示
            isRequestError = true
            uiStateManager.showErrorToast(message)
        }
    }

    func successToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = false
            uiStateManager.showSuccessToast(message)
        }
    }

    // MARK: - 性能优化方法

    /// 处理消息数量变化的防抖动逻辑
    func handleMessageCountChange(newCount: Int, action: @escaping () -> Void) {
        // 防止频繁触发
        guard newCount != lastMessageCount else { return }
        lastMessageCount = newCount

        // 使用防抖动机制
        DispatchQueue.main.asyncAfter(deadline: .now() + uiUpdateThrottleInterval) {
            action()
        }
    }

    // MARK: - 超时管理方法

    /// 启动请求超时计时器
    private func startRequestTimeoutTimer() {
        stopRequestTimeoutTimer() // 先停止之前的计时器

        requestTimeoutTimer = Timer.scheduledTimer(withTimeInterval: requestTimeoutInterval, repeats: false) { [weak self] _ in
            guard let self = self else { return }

            logger.warning("请求超时，自动断开连接")

            // 断开连接
            chatManager.disconnect()

            // 重置状态并显示超时错误
            DispatchQueue.main.async {
                self.stopRequestTimeoutTimer()
                self.failedToast("请求超时，请检查网络连接后重试")
            }
        }
    }

    /// 停止请求超时计时器
    private func stopRequestTimeoutTimer() {
        requestTimeoutTimer?.invalidate()
        requestTimeoutTimer = nil
    }

    /// 处理最后一条消息内容变化的防抖动逻辑
    func handleLastMessageContentChange(newContent: String?, action: @escaping () -> Void) {
        let now = Date()
        guard now.timeIntervalSince(lastContentUpdateTime) > uiUpdateThrottleInterval else { return }
        lastContentUpdateTime = now

        // 取消之前的更新任务
        contentUpdateWorkItem?.cancel()

        // 创建新的更新任务
        let workItem = DispatchWorkItem {
            action()
        }
        contentUpdateWorkItem = workItem

        // 延迟执行
        DispatchQueue.main.asyncAfter(deadline: .now() + uiUpdateThrottleInterval, execute: workItem)
    }

    /// 批量更新消息以减少重渲染
    func batchUpdateMessages(_ updates: [() -> Void]) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }

            // 暂时禁用动画
            UIView.setAnimationsEnabled(false)

            // 执行所有更新
            updates.forEach { $0() }

            // 重新启用动画
            UIView.setAnimationsEnabled(true)
        }
    }

    /// 清理内存和缓存
    func cleanupMemory() {
        // 重置滚动状态
        scrollManager.resetScrollState()

        logger.info("内存清理完成")
    }
}
